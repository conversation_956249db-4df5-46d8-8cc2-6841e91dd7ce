#!/usr/bin/env python3
"""
Test script to verify browser agent improvements for:
1. Complete raw requests with full headers and bodies
2. Enhanced network request logging
3. LLM interaction logging and cost tracking
"""

import asyncio
import json
from pathlib import Path
import sys

# Add parent directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

from preprocessor.browser_agent import BrowserAgent
from shared.config import get_vapt_config

async def test_browser_agent_improvements():
    """Test the enhanced browser agent functionality"""
    
    print("🧪 Testing Browser Agent Improvements")
    print("=" * 50)
    
    # Initialize browser agent
    config = get_vapt_config()
    agent = BrowserAgent(config)
    
    # Test URL - using a simple test site
    test_url = "https://httpbin.org/forms/post"
    
    print(f"🎯 Target URL: {test_url}")
    print(f"🔧 LLM Logging Available: {hasattr(agent, 'llm_logger') and agent.llm_logger is not None}")
    
    try:
        # Run reconnaissance
        print("\n🚀 Starting reconnaissance...")
        report_path = await agent.run_reconnaissance(test_url)
        
        print(f"\n✅ Reconnaissance completed!")
        print(f"📄 Report saved to: {report_path}")
        
        # Analyze the report
        print("\n📊 Analyzing report improvements...")
        with open(report_path, 'r', encoding='utf-8') as f:
            report_data = json.load(f)
        
        # Check improvements
        improvements_found = []
        
        # 1. Check LLM interactions
        if "llm_interactions" in report_data and report_data["llm_interactions"]:
            improvements_found.append(f"✅ LLM Interactions: {len(report_data['llm_interactions'])} logged")
            if "llm_cost_usd" in report_data["metadata"]:
                improvements_found.append(f"✅ LLM Cost Tracking: ${report_data['metadata']['llm_cost_usd']:.6f}")
        else:
            improvements_found.append("❌ LLM Interactions: Not captured")
        
        # 2. Check enhanced network logs
        if "network_logs" in report_data and report_data["network_logs"]:
            network_logs = report_data["network_logs"]
            improvements_found.append(f"✅ Network Logs: {len(network_logs)} requests")
            
            # Check for enhanced fields
            sample_log = network_logs[0] if network_logs else {}
            enhanced_fields = ["domain", "path", "query_params", "headers", "timing"]
            found_fields = [field for field in enhanced_fields if field in sample_log]
            improvements_found.append(f"✅ Enhanced Network Fields: {', '.join(found_fields)}")
        else:
            improvements_found.append("❌ Network Logs: Not captured")
        
        # 3. Check enhanced raw requests
        if "raw_request" in report_data and report_data["raw_request"]:
            raw_requests = report_data["raw_request"]
            improvements_found.append(f"✅ Raw Requests: {len(raw_requests)} captured")
            
            # Check for complete HTTP format
            sample_request = raw_requests[0] if raw_requests else {}
            if "headers" in sample_request and "body" in sample_request:
                improvements_found.append("✅ Complete HTTP Data: Headers and body captured")
            if "response_headers" in sample_request and "response_body" in sample_request:
                improvements_found.append("✅ Response Data: Headers and body captured")
        else:
            improvements_found.append("❌ Raw Requests: Not captured")
        
        # 4. Check enhanced console logs
        if "console_logs" in report_data and report_data["console_logs"]:
            console_logs = report_data["console_logs"]
            improvements_found.append(f"✅ Console Logs: {len(console_logs)} messages")
            
            # Check for enhanced fields
            sample_console = console_logs[0] if console_logs else {}
            enhanced_fields = ["url", "line", "column", "raw_line"]
            found_fields = [field for field in enhanced_fields if field in sample_console and sample_console[field]]
            if found_fields:
                improvements_found.append(f"✅ Enhanced Console Fields: {', '.join(found_fields)}")
        else:
            improvements_found.append("❌ Console Logs: Not captured")
        
        # 5. Check network statistics
        if "network_statistics" in report_data:
            stats = report_data["network_statistics"]
            improvements_found.append(f"✅ Network Statistics: {stats.get('total_requests', 0)} requests analyzed")
        else:
            improvements_found.append("❌ Network Statistics: Not generated")
        
        # 6. Check raw outputs
        if "raw_network_output" in report_data and "raw_console_output" in report_data:
            improvements_found.append("✅ Raw Outputs: Network and console data preserved")
        else:
            improvements_found.append("❌ Raw Outputs: Not preserved")
        
        # Print results
        print("\n🔍 Improvement Analysis Results:")
        print("-" * 40)
        for improvement in improvements_found:
            print(f"  {improvement}")
        
        # Summary
        success_count = len([i for i in improvements_found if i.startswith("✅")])
        total_count = len(improvements_found)
        
        print(f"\n📈 Overall Score: {success_count}/{total_count} improvements working")
        
        if success_count >= total_count * 0.8:
            print("🎉 Excellent! Most improvements are working correctly.")
        elif success_count >= total_count * 0.6:
            print("👍 Good! Most improvements are working, some may need attention.")
        else:
            print("⚠️  Several improvements need attention.")
        
        return report_path
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return None

def main():
    """Main test function"""
    print("Browser Agent Improvements Test")
    print("Testing enhanced network logging, raw requests, and LLM interaction tracking")
    print()
    
    # Run the test
    report_path = asyncio.run(test_browser_agent_improvements())
    
    if report_path:
        print(f"\n✅ Test completed successfully!")
        print(f"📄 Detailed report available at: {report_path}")
        print("\n💡 Next steps:")
        print("  1. Review the generated report for completeness")
        print("  2. Check LLM_logs/ directory for interaction logs")
        print("  3. Verify network requests include full headers and bodies")
        print("  4. Confirm console logs capture detailed error information")
    else:
        print("\n❌ Test failed. Please check the error messages above.")

if __name__ == "__main__":
    main()
