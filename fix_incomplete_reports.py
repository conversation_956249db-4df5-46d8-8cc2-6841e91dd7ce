#!/usr/bin/env python3
"""
Fix Incomplete Reports

This script reprocesses existing reports to extract missing interaction data
using improved parsing methods.
"""

import json
import sys
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, List

# Add current directory to path
sys.path.insert(0, str(Path(__file__).parent))

def enhanced_parse_interactions(agent_result: str, url: str) -> Dict[str, Any]:
    """Enhanced parsing method to extract interactions from agent results."""
    lines = agent_result.lower().split('\n')
    
    component_interactions = []
    form_submissions = []
    scroll_interactions = []
    interaction_counter = 0
    
    # Enhanced patterns for better interaction detection
    click_patterns = [
        "clicking", "clicked", "click", "button interaction", "interacting with",
        "pressing", "pressed", "selecting", "selected", "activating", "activated"
    ]
    
    form_patterns = [
        "submitting form", "form submission", "submit", "filling form", "form data",
        "testing forms", "contact form", "newsletter", "input field", "form validation"
    ]
    
    scroll_patterns = [
        "scrolling", "scroll", "scrolled", "scroll through", "scroll action",
        "phases: top, middle, and bottom", "revealing content"
    ]
    
    navigation_patterns = [
        "navigate", "navigating", "redirected", "redirect", "page to", "loading",
        "returned to", "back to", "browser_navigate"
    ]
    
    for line in lines:
        line_stripped = line.strip()
        if not line_stripped:
            continue
            
        ts = datetime.now().isoformat()
        
        # Check for click interactions
        if any(pattern in line for pattern in click_patterns):
            interaction_counter += 1
            interaction_type = "click"
            if "button" in line:
                interaction_type = "button_click"
            elif "link" in line:
                interaction_type = "link_click"
            
            component_interactions.append({
                "url": url, 
                "timestamp": ts, 
                "type": interaction_type, 
                "details": line_stripped
            })
        
        # Check for form interactions
        elif any(pattern in line for pattern in form_patterns):
            form_submissions.append({
                "url": url, 
                "timestamp": ts, 
                "type": "form_interaction",
                "details": line_stripped
            })
        
        # Check for scroll interactions
        elif any(pattern in line for pattern in scroll_patterns):
            scroll_interactions.append({
                "url": url, 
                "timestamp": ts, 
                "type": "scroll_action",
                "details": line_stripped
            })
        
        # Check for navigation interactions
        elif any(pattern in line for pattern in navigation_patterns):
            interaction_counter += 1
            component_interactions.append({
                "url": url, 
                "timestamp": ts, 
                "type": "navigation", 
                "details": line_stripped
            })
    
    return {
        "component_interactions": component_interactions,
        "form_submissions": form_submissions,
        "scroll_interactions": scroll_interactions,
        "interaction_counter": interaction_counter
    }

def fix_report(report_path: str) -> bool:
    """Fix an incomplete report by reprocessing the agent results."""
    try:
        print(f"🔧 Processing report: {report_path}")
        
        # Load the report
        with open(report_path, 'r', encoding='utf-8') as f:
            report_data = json.load(f)
        
        # Check if it needs fixing
        current_interactions = report_data.get("metadata", {}).get("total_interactions", 0)
        current_components = len(report_data.get("component_interactions", []))
        current_forms = len(report_data.get("form_submissions", []))
        
        print(f"📊 Current state:")
        print(f"   - Total interactions: {current_interactions}")
        print(f"   - Component interactions: {current_components}")
        print(f"   - Form submissions: {current_forms}")
        
        if current_interactions > 0 and current_components > 0:
            print("✅ Report appears complete, skipping")
            return False
        
        # Extract agent results
        recon_data = report_data.get("recon", [])
        if not recon_data:
            print("❌ No reconnaissance data found to reprocess")
            return False
        
        # Combine all recon text
        combined_text = "\n".join(recon_data)
        target_url = report_data.get("metadata", {}).get("target_url", "")
        
        print("🔍 Reprocessing agent results...")
        
        # Reprocess with enhanced parsing
        parsed_data = enhanced_parse_interactions(combined_text, target_url)
        
        # Update the report
        report_data["component_interactions"] = parsed_data["component_interactions"]
        report_data["form_submissions"] = parsed_data["form_submissions"]
        
        # Merge scroll interactions (keep existing + add new)
        existing_scrolls = report_data.get("scroll_interactions", [])
        new_scrolls = parsed_data["scroll_interactions"]
        all_scrolls = existing_scrolls + new_scrolls
        report_data["scroll_interactions"] = all_scrolls
        
        # Update metadata
        if "metadata" in report_data:
            report_data["metadata"]["total_interactions"] = parsed_data["interaction_counter"]
            report_data["metadata"]["total_forms_submitted"] = len(parsed_data["form_submissions"])
            report_data["metadata"]["total_scroll_actions"] = len(all_scrolls)
            report_data["metadata"]["reprocessed"] = True
            report_data["metadata"]["reprocessed_timestamp"] = datetime.now().isoformat()
        
        # Add processing summary to detailed logs
        processing_summary = {
            'timestamp': datetime.now().isoformat(),
            'level': 'INFO',
            'url': target_url,
            'action': 'report_reprocessing',
            'details': f'Reprocessed report with enhanced parsing. Found {parsed_data["interaction_counter"]} interactions.',
            'parsing_summary': {
                'interactions_found': len(parsed_data["component_interactions"]),
                'forms_found': len(parsed_data["form_submissions"]),
                'scrolls_found': len(parsed_data["scroll_interactions"])
            }
        }
        
        if "detailed_logs" not in report_data:
            report_data["detailed_logs"] = []
        report_data["detailed_logs"].append(processing_summary)
        
        # Create backup of original
        backup_path = report_path.replace('.json', '_backup.json')
        with open(backup_path, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, indent=2, ensure_ascii=False)
        
        # Save the fixed report
        print(f"💾 Saving fixed report to: {report_path}")
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, indent=2, ensure_ascii=False)

        # Verify the save worked
        with open(report_path, 'r', encoding='utf-8') as f:
            verify_data = json.load(f)
        verify_interactions = verify_data.get("metadata", {}).get("total_interactions", 0)
        print(f"🔍 Verification: saved file shows {verify_interactions} interactions")
        
        print(f"✅ Fixed report saved!")
        print(f"📊 New state:")
        print(f"   - Total interactions: {parsed_data['interaction_counter']}")
        print(f"   - Component interactions: {len(parsed_data['component_interactions'])}")
        print(f"   - Form submissions: {len(parsed_data['form_submissions'])}")
        print(f"   - Scroll actions: {len(all_scrolls)}")
        print(f"💾 Backup saved to: {backup_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error processing {report_path}: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main function to fix incomplete reports."""
    print("🔧 Report Fixing Utility")
    print("=" * 40)
    
    reports_dir = Path("reports")
    if not reports_dir.exists():
        print("❌ Reports directory not found")
        return
    
    # Find recent reports
    json_files = list(reports_dir.glob("*.json"))
    json_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
    
    if not json_files:
        print("❌ No JSON reports found")
        return
    
    print(f"📁 Found {len(json_files)} report files")
    
    # Process the most recent reports
    fixed_count = 0

    # Focus on the specific incomplete report
    target_report = "reports/recon_report_brokencrystals.com_20250729_102105.json"
    if Path(target_report).exists():
        print(f"🎯 Targeting specific report: {target_report}")
        if fix_report(target_report):
            fixed_count += 1
        print()

    # Also process other recent reports
    for report_file in json_files[:3]:  # Process last 3 reports
        if str(report_file) != target_report:  # Skip if already processed
            if fix_report(str(report_file)):
                fixed_count += 1
            print()
    
    print(f"🎉 Processing complete! Fixed {fixed_count} reports.")

if __name__ == "__main__":
    main()
